package scrbg.meplat.mall.config.scheduled.plan;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.service.plan.PlanService;

@Component
@ConditionalOnProperty(name="app.schedule.enable",havingValue = "true")
@RequiredArgsConstructor
@Slf4j
public class PlanConsumer {

    private final PlanService planService;
    private final StringRedisTemplate redisTemplate;
    private final PlanStateHandler planStateHandler;

    private static final int WORKER_COUNT = 1;
    private final Executor planExecutor = Executors.newFixedThreadPool(WORKER_COUNT);

    private static final int MAX_RETRY = 1;

    @PostConstruct
    public void startConsumers() {
        for (int i = 0; i < WORKER_COUNT; i++) {
            planExecutor.execute(this::consumeLoop);
        }
    }

    private void consumeLoop() {
        while (true) {
            String billId = null;
            try {
                billId = redisTemplate.opsForList().rightPop(PlanProducer.PLAN_QUEUE_KEY, 5, TimeUnit.SECONDS);
                if (billId == null) {
                    continue; // 队列空，继续轮询
                }
                handlePlan(billId);
            } catch (Exception e) {
                if (billId==null) {
                    log.error("从redis队列中获取计划id失败", e);
                }else {
                    log.error("处理计划失败, 计划id: " + billId, e);
                }
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException ignored) {}
            }
        }
    }

    private void handlePlan(String billId) {
        Plan plan = planService.getById(billId);
        if (plan == null || !"1".equals(plan.getState())) {
            return; // 状态不匹配，跳过
        }
        boolean flag = false;
        // 失败则重试
        for (int attempt = 1; attempt <= MAX_RETRY; attempt++) {
            try {
                planStateHandler.handlePlan(plan);
                flag = true;
                break;
            } catch (Exception e) {
                log.error("第 " + attempt + " 次异常: ", e);
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException ignored) {}
            }
        }
        if (!flag) {
            log.warn("计划 " + billId + " 重试失败，放弃");
        }
    }

}

