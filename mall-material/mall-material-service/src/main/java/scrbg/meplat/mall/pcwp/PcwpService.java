package scrbg.meplat.mall.pcwp;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.multipart.MultipartFile;

import scrbg.meplat.mall.dto.plan.BulkRetailPlanEX;
import scrbg.meplat.mall.entity.MaterialDemand;
import scrbg.meplat.mall.pcwp.auth.model.SignUp;
import scrbg.meplat.mall.pcwp.auth.model.SupplierData;
import scrbg.meplat.mall.pcwp.auth.model.SupplierRes;
import scrbg.meplat.mall.pcwp.auth.model.TokenRes;
import scrbg.meplat.mall.pcwp.dto.PcwpAcceptanceRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpRevolAcceptanceDto;
import scrbg.meplat.mall.pcwp.dto.PcwpSaveSiteReceivingRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpSiteReceiptRequest;
import scrbg.meplat.mall.pcwp.org.model.Org;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.Material;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;
import scrbg.meplat.mall.pcwp.third.model.ReconciliationDtl;
import scrbg.meplat.mall.pcwp.third.model.RetailPlan;
import scrbg.meplat.mall.pcwp.third.model.RevolPlan;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanEx;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanQueryResult;
import scrbg.meplat.mall.pcwp.third.model.StatePlan;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.pcwp.third.model.VerifyPlan;

/**
 * 利用openfeign客户端调用pcwp接口
 * 维护各个接口里需要的固定传值，比如header里的sysCode,只保留必要的参数从外部传入
 * token如果可以与当前线程绑定的话，也可以不通过传参的方式传入
 */
public interface PcwpService {
    /**
     * 登录
     * @param account
     * @param password
     * @return
     */
    PcwpRes<TokenRes>  signIn(String account,String password);
    
    /**
     * 使用旧密码修改用户密码
     * @param userId
     * @param oldPwd
     * @param newPwd
     * @param token
     * @return
     */
    PcwpRes<Void> changeUserPassword(String userId,String oldPwd,String newPwd,String token);



    /**
     * 认证-创建外部用户Token
     *
     * @param phoneNo 手机号
     * @return Token响应
     */
    PcwpRes<TokenRes> createExternalToken(String phoneNo);



    /**
     * 第三方接口-根据供应商信用代码查询供应商基础信息
     *
     * @param creditCode 信用代码
     * @return 供应商信息
     */
    PcwpRes<SupplierRes> getSupplierByCreditCode(String creditCode);


    /**
     * 身份认证-用户-机构注册
     *
     * @param signUp 机构信息
     * @return
     */
    PcwpRes<Boolean> userOrgSignUp(SignUp signUp);

 
    /**
     * 组织机构-获取用户所在机构
     * @param userId
     * @return
     */
    PcwpRes<List<Org>> getOrgByUserId(String userId);

    /**
     * 组织机构-通过机构Id查找机构
     * @param orgId
     * @return
     */
    PcwpRes<Org> getOrgById(String orgId);

    /**
     * 组织机构-获取用户所在机构分配的角色
     * @param userId
     * @param org
     * @return
     */
    PcwpRes<List<String>> getUserHasRoles(String userId, Org org, String token);
    /**
     *  第三方接口服务-保存推送的大宗零购计划(提供给物资采购平台)
     * @param  BulkRetailPlan
     * @return
     */
    PcwpRes<String> saveBulkRetailPlan(KeyedPayload<BulkRetailPlanEX> BulkRetailPlan);

    /**
     *  第三方接口服务-回滚大宗零购计划(提供给物资贸易平台)
     * @param  BulkRetailPlan
     * @return
     */
    PcwpRes<Void> rollbackBulkRetailPlan(String keyId);

    /**
     * 第三方接口服务-根据大宗零购计划id获取大宗零购计划详情
     * @param payload
     * @return
     */
    PcwpRes<StatePlan> getBulkRetailPlanById(String id);
    /**
     * 第三方接口服务-分页查询大宗临购计划
     * @param payload
     * @return
     */
    PcwpRes<PcwpPageRes<BulkRetailPlanPageQueryResult>> queryPageBulkRetailPlan(BulkRetailPlanPageQueryCondition filter);

    /**
     * 第三方接口服务-反写大宗临购计划商城订单数量
     * @param payload
     * @return
     */
    PcwpRes<Void> updateBulkRetailPlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload);

    /**
     * 第三方接口服务-通过分页加载物资信息（提供给物资采购平台）
     * @param materialPageDto
     * @return
     */
    PcwpPageRes<Material> queryPageMaterialDtl(MaterialPageDto materialPageDto);
    /**
     * 第三方接口服务-验证PCWP总计划是否有同名称、同规格、且有剩余数量的物资
     * @param verifyPlan
     * @return
     */
    PcwpRes<Boolean> verifyPlan(VerifyPlan verifyPlan);

    /**
     * 第三方接口服务-判断是否能做对账单（提供给物资采购平台）
     *
     * @param orgId 组织ID
     * @param date  日期（格式：yyyy/MM/dd）
     * @return 是否可以操作对账单
     */
    PcwpRes<Boolean> isCanOperaBill(String orgId, String date);

    /**
     * 第三方接口服务-周转材料判断是否能做对账单（提供给物资采购平台）
     * @param orgId
     * @param date
     * @return
     */
    PcwpRes<Boolean> isRevolCanOperaBill(String orgId, String date);

    /**
     * 第三方接口服务-保存验收单（提供给物资采购平台）
     *
     * @param request 验收单请求对象
     * @return 保存结果
     */
    PcwpRes<String> saveAcceptance(PcwpAcceptanceRequest request);

    /**
     * 第三方接口服务-清除关系ID（提供给物资采购平台）
     *
     * @param relationId 验收单ID
     * @param orgId      组织ID
     * @return 清除结果
     */
    PcwpRes<Boolean> clearRelationId(String relationId, String orgId);

    /**
     * 第三方接口服务-检查外部单据是否能作废（提供给物资采购平台）
     *
     * @param billId 验收单id
     * @param orgId  机构id
     * @return 是否可以作废
     */
    PcwpRes<Boolean> checkOutBillCanBeInvalidated(String billId, String orgId);

    /**
     * 第三方接口服务-检查外部单据是否能作废（周转材料专用）
     *
     * @param billId 验收单id
     * @param orgId  机构id
     * @return 是否可以作废
     */
    PcwpRes<Boolean> checkOutBillCanBeInvalidatedForRevol(String billId, String orgId);

    /**
     * 保存周转材料验收单（提供给物资采购平台）
     *
     * @param request 周转材料验收单请求对象
     * @return 保存结果
     */
    PcwpRes<String> saveAcceptanceForRevol(KeyedPayload<PcwpRevolAcceptanceDto> request);

    /**
     * 第三方接口服务-回滚保存验收单（提供给物资采购平台）
     *
     * @param keyId 操作keyId
     * @return 回滚结果
     */
    PcwpRes<Void> rollBackSaveAcceptance(String keyId);

    /**
     * 第三方接口服务-推送零星采购计划
     * @param plan
     * @return
     */
    PcwpRes<String> savePlan(KeyedPayload<RetailPlan> plan);

    /**
     * 第三方接口服务-回滚保存推送的计划
     * @param keyId
     * @return
     */
    PcwpRes<Void> rollbackPlan(String keyId);

    /**
     * 第三方接口服务-反写零星采购计划商城订单数量(提供给物资采购平台)
     * @param payload
     * @return
     */
    PcwpRes<Void> updateRetailPlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload);

    /**
     * 第三方接口服务-根据零星采购计划id获取零星采购计划
     * @param payload
     * @return
     */
    PcwpRes<SporadicPurchasePlanEx> getRetailPlanById(String id);

    /**
     * 第三方接口服务-分页查询零星采购计划
     * @param payload
     * @return
     */
    public PcwpRes<PcwpPageRes<SporadicPurchasePlanQueryResult>> queryRetailPlansPage(SporadicPurchasePlanPageQueryCondition filter);

    /**
     *  第三方接口服务-保存推送的周转材料计划(提供给物资采购平台)
     * @param BulkRetailPlan
     * @return
     */
    PcwpRes<String> saveRevolRetailPlan(RevolPlan BulkRetailPlan);

    /**
     * 第三方接口服务-周才数据回滚 (推送验收,推送计划,反写暂扣及暂扣作废)(物资采购平台)
     * @param keyId
     * @return
     */
    PcwpRes<Void> rollbackRevolPlan(String keyId);
    
    /**
     *  第三方接口服务-分页查询周转材料计划
     * @param BulkRetailPlan
     * @return
     */
    PcwpRes<PcwpPageRes<RevolPlanPageQueryResult>> queryPageRevolPlan(RevolPlanPageQueryCondition filter);

    /**
     * 第三方接口服务-反写周转材料计划商城订单数量
     * @param payload
     * @return
     */
    PcwpRes<Void> updateRevolPlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload);

    /**
     * 第三方接口服务-根据周转材料计划id获取周转材料计划
     * @param payload
     * @return
     */
    PcwpRes<List<StatePlan.Detail>> getRevolRetailPlanById(String id);

    /**
     * 1、获取可对账的物资(提供给物资采购平台)
     *
     * @param request 获取可对账的物资请求
     * @return 可对账物资列表
     */
    PcwpRes<Map> getCanUseSiteReceivingDtl(PcwpAcceptanceRequest.PcwpAcceptanceData request);

    /**
     * 1-1、获取可对账的物资(提供给物资采购平台) -周转材料
     *
     * @param request 获取可对账的物资请求
     * @return 可对账物资列表
     */
    PcwpRes<Map> getReconcileableItem(PcwpAcceptanceRequest.PcwpAcceptanceData request);

    /**
     * 2、回滚保存现场收料(提供给物资采购平台)
     *
     * @param keyId 接口调用key
     * @return 回滚结果
     */
    PcwpRes<Map> rollBackSaveSiteReceiving(String keyId);

    /**
     * 3、回滚反写对账单暂扣数量(提供给物资采购平台)
     *
     * @param keyId 接口调用key
     * @return 回滚结果
     */
    PcwpRes<Map> rollBackWriteBackBillLockQuantiy(String keyId);

    /**
     * 4、回滚反写已审核对账单数量(提供给物资采购平台)
     *
     * @param keyId 接口调用key
     * @return 回滚结果
     */
    PcwpRes<Map> rollBackWriteBackBillQuantiy(String keyId);

    /**
     * 5、保存现场收料(提供给物资采购平台)
     *
     * @param request 保存现场收料请求数据
     * @return 保存结果
     */
    PcwpRes<Map> saveSiteReceiving(PcwpSaveSiteReceivingRequest request);

    /**
     * 6、反写对账单暂扣数量(提供给物资采购平台)
     *
     * @param payload KeyedPayload格式的反写数据
     * @return 反写结果
     */
    PcwpRes<Void> writeBackBillLockQuantiy(KeyedPayload<List<ReconciliationDtl>> payload);

    /**
     * 7、反写现场收料数量（提供给物资采购平台）
     *
     * @param payload KeyedPayload格式的反写数据
     * @return 反写结果
     */
    PcwpRes<Void> writeBackBillQuantiy(KeyedPayload<List<ReconciliationDtl>> payload);

    /**
     * PCWP2.0-组织机构服务
     *获取用户的所有角色
    * */
    PcwpRes<Map<String, Object>> getPersonPermissons(@RequestBody Map<String,Object> params,
                                     @RequestHeader(value = "token") String token,
                                     @RequestHeader(value = "sysCode") String sysCode,
                                     @RequestHeader(value = "org") String org);
    /**
     * 第三方接口服务-新增-修改 供应商
     * @param supplierData
     * @return
     */
    PcwpRes<String> pushByShop(SupplierData supplierData);


    PcwpRes<List<MaterialDemand>> findDtlByBillId(String billId);

    /**
     * 第三方接口服务-对象上传
     * @param files
     * @param relationId
     * @return
     */
    PcwpRes<Void> materialUploade(List<MultipartFile> files, String relationId, String shortCode);
}
